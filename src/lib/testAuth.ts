import { supabase } from '@/integrations/supabase/client';

/**
 * Test authentication helper for development purposes
 */
export class TestAuth {
  /**
   * Create a test user for development
   */
  static async createTestUser(email: string, password: string, name: string, role: 'brand' | 'creator' = 'brand') {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: name,
            role: role,
          }
        }
      });

      if (error) {
        console.error('Error creating test user:', error);
        throw error;
      }

      console.log('Test user created successfully:', data.user?.email);
      return data;
    } catch (error) {
      console.error('Failed to create test user:', error);
      throw error;
    }
  }

  /**
   * Sign in with test credentials
   */
  static async signInTestUser(email: string, password: string) {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('Error signing in test user:', error);
        throw error;
      }

      console.log('Test user signed in successfully:', data.user?.email);
      return data;
    } catch (error) {
      console.error('Failed to sign in test user:', error);
      throw error;
    }
  }

  /**
   * Get current session
   */
  static async getCurrentSession() {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error('Error getting session:', error);
        throw error;
      }

      return session;
    } catch (error) {
      console.error('Failed to get session:', error);
      throw error;
    }
  }

  /**
   * Sign out current user
   */
  static async signOut() {
    try {
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        console.error('Error signing out:', error);
        throw error;
      }

      console.log('User signed out successfully');
    } catch (error) {
      console.error('Failed to sign out:', error);
      throw error;
    }
  }
}

// Development helper functions
if (typeof window !== 'undefined') {
  // Make TestAuth available in browser console for development
  (window as any).TestAuth = TestAuth;
  
  // Add some quick test functions
  (window as any).createTestUser = () => TestAuth.createTestUser('<EMAIL>', 'password123', 'Test User', 'brand');
  (window as any).signInTestUser = () => TestAuth.signInTestUser('<EMAIL>', 'password123');
  (window as any).getCurrentSession = () => TestAuth.getCurrentSession();
  (window as any).signOut = () => TestAuth.signOut();
  
  console.log('Test auth helpers available:');
  console.log('- createTestUser() - Create a test user');
  console.log('- signInTestUser() - Sign in test user');
  console.log('- getCurrentSession() - Get current session');
  console.log('- signOut() - Sign out current user');
}
