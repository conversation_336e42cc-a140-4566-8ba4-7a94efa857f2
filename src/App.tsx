import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./hooks/useAuth";
import Index from "./pages/Index";
import Dashboard from "./pages/Dashboard";
import CreatorDashboard from "./pages/CreatorDashboard";
import CreatorProfile from "./pages/CreatorProfile";
import Settings from "./pages/Settings";
import NotFound from "./pages/NotFound";

// Import components from new locations
import { CampaignDetail, CreateCampaign, Campaigns } from "@/components/dashboard/campaigns";
import { InfluencerProfile, Influencers } from "@/components/dashboard/influencers";
import { Outreach } from "@/components/dashboard/outreach";
import { BrandProfile } from "@/components/dashboard/profile";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/creator-dashboard" element={<CreatorDashboard />} />
            <Route path="/creator-profile" element={<CreatorProfile />} />
            <Route path="/settings" element={<Settings />} />

            {/* Campaign Routes */}
            <Route path="/campaigns/:id" element={<CampaignDetail />} />
            <Route path="/campaigns/create" element={<CreateCampaign />} />
            <Route path="/campaigns" element={<Campaigns />} />

            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
