// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://veeuscozuavvqyjqeljq.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZlZXVzY296dWF2dnF5anFlbGpxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1OTcxODIsImV4cCI6MjA2NDE3MzE4Mn0.r2WunFzzv0PwGblLy0CcGvRzgbO1YjVn5hO8lLzQp2s";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);